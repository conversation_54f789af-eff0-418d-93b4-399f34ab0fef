import { adminClient, usernameClient } from "better-auth/client/plugins";
import { createAuthClient } from "better-auth/react";

const baseURL = import.meta.env.VITE_API_BASE_URL || "http://localhost:3000";
const callbackURL = baseURL + "/callback";

export const authClient = createAuthClient({
    baseURL: `${baseURL}/auth`,
    plugins: [adminClient(), usernameClient()],
    fetchOptions: {
        onError: (ctx) => {
            console.error("Auth Error:", ctx.error);
        },
    },
});

export const signInDiscord = async () => {
    const data = await authClient.signIn.social({
        provider: "discord",
        callbackURL: `${callbackURL}?auth=discord`,
    });
    return data;
};

export const signInGoogle = async () => {
    const data = await authClient.signIn.social({
        provider: "google",
        callbackURL: `${callbackURL}?auth=google`,
    });
    return data;
};

// Export commonly used hooks and methods
export const { useSession, signIn, signOut, signUp, forgetPassword, resetPassword, changePassword, changeEmail } =
    authClient;
