// Based on the publicConfig from backend
export interface GameConfig {
    version: number;
    // Auth config
    REGISTRATION_DISABLED: boolean;
    REGISTRATION_CODES_DISABLED: boolean;
    LOGIN_DISABLED: boolean;
    DISCORD_AUTH_ENABLED: boolean;
    GOOGLE_AUTH_ENABLED: boolean;

    // User config
    MAX_LEVEL_CAP: number;
    HEALING_TICK_INTERVAL: number;
    AP_TICK_INTERVAL: number;
    AP_TICK_INTERVAL_MINUTES: number;
    ENERGY_TICK_MS: number;
    HEALTH_REGEN_AMOUNT: number;

    // Battle config
    PVP_MIN_LVL: number;
    PVP_BATTLE_AP_COST: number;
    ROOFTOP_BATTLE_AP_COST: number;

    // Other commonly used configs
    CASINO_DISABLED: boolean;
    CHAT_DISABLED: boolean;
    BANK_DISABLED: boolean;
    CRAFTING_ENERGY_COST: number;

    MARQUEE_BANNER_DISABLED: boolean;

    // Level gates
    SHOP1_LEVEL_GATE: number;
    SHOP2_LEVEL_GATE: number;
    SHOP3_LEVEL_GATE: number;
    JOBS_LEVEL_GATE: number;
    CRAFTING_LEVEL_GATE: number;
    TALENTS_LEVEL_GATE: number;
    DAILY_QUESTS_LEVEL_GATE: number;
    COURSES_LEVEL_GATE: number;
    ROOFTOP_BATTLES_LEVEL_GATE: number;
    MARKET_LEVEL_GATE: number;
    ARCADE_LEVEL_GATE: number;
    // Allow for other properties in the config
    [key: string]: any;
}
